import { NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';
import { createServiceClient } from '../../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for category updates
const categoryUpdateSchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name too long').optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  image: z.string().url().optional().or(z.literal('')),
  parent_id: z.string().uuid().optional().nullable(),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServiceClient();
    const { id } = params;

    const { data: category, error } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        image,
        parent_id,
        sort_order,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error: any) {
    console.error('Error in category GET API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const { id } = params;
    const body = await request.json();
    
    // Validate input data
    const validationResult = categoryUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Generate slug if name is being updated and slug is not provided
    if (updateData.name && !updateData.slug) {
      updateData.slug = generateSlug(updateData.name);
    }

    // Check if slug already exists (excluding current category)
    if (updateData.slug) {
      const { data: existingCategory } = await supabase
        .from('categories')
        .select('id')
        .eq('slug', updateData.slug)
        .neq('id', id)
        .single();

      if (existingCategory) {
        return NextResponse.json(
          { error: 'Category with this name already exists' },
          { status: 409 }
        );
      }
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();
    const { data: category, error } = await serviceSupabase
      .from('categories')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        id,
        name,
        slug,
        description,
        image,
        parent_id,
        sort_order,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .single();

    if (error) {
      console.error('Error updating category:', error);
      return NextResponse.json(
        { error: 'Failed to update category' },
        { status: 500 }
      );
    }

    return NextResponse.json(category);
  } catch (error: any) {
    console.error('Error in category PUT API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const { id } = params;

    // Check if category has products
    const serviceSupabase = createServiceClient();
    const { data: products, error: productsError } = await serviceSupabase
      .from('products')
      .select('id')
      .eq('category_id', id)
      .limit(1);

    if (productsError) {
      console.error('Error checking products:', productsError);
      return NextResponse.json(
        { error: 'Failed to check category usage' },
        { status: 500 }
      );
    }

    if (products && products.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that has products. Please move or delete products first.' },
        { status: 409 }
      );
    }

    // Check if category has child categories
    const { data: children, error: childrenError } = await serviceSupabase
      .from('categories')
      .select('id')
      .eq('parent_id', id)
      .limit(1);

    if (childrenError) {
      console.error('Error checking child categories:', childrenError);
      return NextResponse.json(
        { error: 'Failed to check category children' },
        { status: 500 }
      );
    }

    if (children && children.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that has child categories. Please move or delete child categories first.' },
        { status: 409 }
      );
    }

    // Delete the category
    const { error } = await serviceSupabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting category:', error);
      return NextResponse.json(
        { error: 'Failed to delete category' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Category deleted successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error in category DELETE API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
