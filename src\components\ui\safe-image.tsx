"use client";

import Image from "next/image";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Package, ImageIcon } from "lucide-react";

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  loading?: "lazy" | "eager";
  sizes?: string;
  fallbackSrc?: string;
  showFallbackIcon?: boolean;
  fallbackIconType?: "package" | "image";
  onError?: () => void;
  onLoad?: () => void;
}

/**
 * SafeImage component with built-in error handling and fallback support
 * Prevents Next.js image configuration errors and provides graceful fallbacks
 */
export function SafeImage({
  src,
  alt,
  width,
  height,
  fill = false,
  className,
  priority = false,
  loading = "lazy",
  sizes,
  fallbackSrc,
  showFallbackIcon = true,
  fallbackIconType = "image",
  onError,
  onLoad,
}: SafeImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Handle image load error
  const handleError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  // Handle image load success
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // Fallback icon component
  const FallbackIcon = fallbackIconType === "package" ? Package : ImageIcon;

  // If there's an error and no fallback src, show fallback UI
  if (imageError && !fallbackSrc) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 text-gray-400",
          fill ? "absolute inset-0" : "",
          className
        )}
        style={!fill ? { width, height } : undefined}
      >
        {showFallbackIcon && (
          <div className="flex flex-col items-center justify-center p-4">
            <FallbackIcon className="h-8 w-8 mb-2" />
            <span className="text-xs text-center">Image not available</span>
          </div>
        )}
      </div>
    );
  }

  // Determine which src to use
  const imageSrc = imageError && fallbackSrc ? fallbackSrc : src;

  return (
    <div className={cn("relative", fill ? "w-full h-full" : "", className)}>
      {/* Loading placeholder */}
      {isLoading && (
        <div
          className={cn(
            "absolute inset-0 bg-gray-200 animate-pulse rounded",
            fill ? "" : "w-full h-full"
          )}
          style={!fill ? { width, height } : undefined}
        />
      )}

      {/* Main image */}
      <Image
        src={imageSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          className
        )}
        priority={priority}
        loading={loading}
        sizes={sizes}
        onError={handleError}
        onLoad={handleLoad}
        // Add unoptimized for external URLs that might not be configured
        unoptimized={src.startsWith('http') && !src.includes('images.unsplash.com')}
      />
    </div>
  );
}

/**
 * ProductImage component specifically for product images with tennis-themed fallback
 */
export function ProductImage({
  src,
  alt,
  className,
  ...props
}: Omit<SafeImageProps, 'fallbackIconType' | 'showFallbackIcon'>) {
  return (
    <SafeImage
      src={src}
      alt={alt}
      className={className}
      fallbackIconType="package"
      showFallbackIcon={true}
      fallbackSrc="/images/product-placeholder.png" // You can add a default product image
      {...props}
    />
  );
}

/**
 * HeroImage component for hero sections with image fallback
 */
export function HeroImage({
  src,
  alt,
  className,
  ...props
}: Omit<SafeImageProps, 'fallbackIconType' | 'showFallbackIcon'>) {
  return (
    <SafeImage
      src={src}
      alt={alt}
      className={className}
      fallbackIconType="image"
      showFallbackIcon={true}
      priority={true}
      {...props}
    />
  );
}
