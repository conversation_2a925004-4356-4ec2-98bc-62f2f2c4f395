/** @type {import('next').NextConfig} */

const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'images.unsplash.com',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: '*.supabase.co',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: 'ik.imagekit.io',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: 'imgs.search.brave.com',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: 'courtsidetennis.com',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: '*.amazonaws.com',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: 'cdn.shopify.com',
                pathname: '/**'
            },
            {
                protocol: 'https',
                hostname: 'via.placeholder.com',
                pathname: '/**'
            }
        ],
    }
};

if (process.env.NEXT_PUBLIC_TEMPO) {
    nextConfig["experimental"] = {
        // NextJS 13.4.8 up to 14.1.3:
        // swcPlugins: [[require.resolve("tempo-devtools/swc/0.86"), {}]],
        // NextJS 14.1.3 to 14.2.11:
        swcPlugins: [[require.resolve("tempo-devtools/swc/0.90"), {}]]

        // NextJS 15+ (Not yet supported, coming soon)
    }
}

module.exports = nextConfig;