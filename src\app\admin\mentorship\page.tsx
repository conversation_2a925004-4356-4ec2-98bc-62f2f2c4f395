"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "../../../components/ui/tabs";
import { StudentManagement } from "../../../components/admin/mentorship/student-management";
import { SessionScheduling } from "../../../components/admin/mentorship/session-scheduling";
import { ResourceManagement } from "../../../components/admin/mentorship/resource-management";
import { StudentCommunication } from "../../../components/admin/mentorship/student-communication";

export default function MentorshipDashboard() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Mentorship Dashboard</h1>
      </div>

      <Tabs defaultValue="students" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
          <TabsTrigger value="chat">Chat</TabsTrigger>
        </TabsList>
        <TabsContent value="students" className="mt-6">
          <StudentManagement />
        </TabsContent>
        <TabsContent value="sessions" className="mt-6">
          <SessionScheduling />
        </TabsContent>
        <TabsContent value="resources" className="mt-6">
          <ResourceManagement />
        </TabsContent>
        <TabsContent value="chat" className="mt-6">
          <StudentCommunication />
        </TabsContent>
      </Tabs>
    </div>
  );
}
