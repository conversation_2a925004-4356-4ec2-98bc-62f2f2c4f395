import { NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';
import { createServiceClient } from '../../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for brand updates
const brandUpdateSchema = z.object({
  name: z.string().min(1, 'Brand name is required').max(100, 'Brand name too long').optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  logo: z.string().url().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  country: z.string().max(100).optional(),
  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServiceClient();
    const { id } = params;

    const { data: brand, error } = await supabase
      .from('brands')
      .select(`
        id,
        name,
        slug,
        description,
        logo,
        website,
        country,
        founded_year,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching brand:', error);
      return NextResponse.json(
        { error: 'Brand not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(brand);
  } catch (error: any) {
    console.error('Error in brand GET API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const { id } = params;
    const body = await request.json();
    
    // Validate input data
    const validationResult = brandUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Generate slug if name is being updated and slug is not provided
    if (updateData.name && !updateData.slug) {
      updateData.slug = generateSlug(updateData.name);
    }

    // Check if slug already exists (excluding current brand)
    if (updateData.slug) {
      const { data: existingBrand } = await supabase
        .from('brands')
        .select('id')
        .eq('slug', updateData.slug)
        .neq('id', id)
        .single();

      if (existingBrand) {
        return NextResponse.json(
          { error: 'Brand with this name already exists' },
          { status: 409 }
        );
      }
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();
    const { data: brand, error } = await serviceSupabase
      .from('brands')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        id,
        name,
        slug,
        description,
        logo,
        website,
        country,
        founded_year,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .single();

    if (error) {
      console.error('Error updating brand:', error);
      return NextResponse.json(
        { error: 'Failed to update brand' },
        { status: 500 }
      );
    }

    return NextResponse.json(brand);
  } catch (error: any) {
    console.error('Error in brand PUT API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const { id } = params;

    // Check if brand has products
    const serviceSupabase = createServiceClient();
    const { data: products, error: productsError } = await serviceSupabase
      .from('products')
      .select('id')
      .eq('brand_id', id)
      .limit(1);

    if (productsError) {
      console.error('Error checking products:', productsError);
      return NextResponse.json(
        { error: 'Failed to check brand usage' },
        { status: 500 }
      );
    }

    if (products && products.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete brand that has products. Please move or delete products first.' },
        { status: 409 }
      );
    }

    // Delete the brand
    const { error } = await serviceSupabase
      .from('brands')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting brand:', error);
      return NextResponse.json(
        { error: 'Failed to delete brand' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Brand deleted successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error in brand DELETE API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
