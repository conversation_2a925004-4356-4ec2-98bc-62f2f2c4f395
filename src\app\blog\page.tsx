"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Calendar, 
  User, 
  Eye, 
  Heart, 
  MessageSquare,
  Clock,
  ArrowRight,
  Filter,
  TrendingUp
} from "lucide-react";
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string | null;
  is_featured: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  tags: string[];
  published_at: string;
  blog_categories?: {
    name: string;
    color: string;
    slug: string;
  } | {
    name: string;
    color: string;
    slug: string;
  }[];
}

interface Category {
  id: string;
  name: string;
  slug: string;
  color: string;
  post_count: number;
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [featuredPosts, setFeaturedPosts] = useState<BlogPost[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const supabase = createClient();

  useEffect(() => {
    fetchCategories();
    fetchFeaturedPosts();
  }, []);

  useEffect(() => {
    fetchPosts();
  }, [searchTerm, selectedCategory, currentPage]);

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('id, name, slug, color, post_count')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchFeaturedPosts = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_articles')
        .select(`
          id, title, slug, excerpt, featured_image, is_featured, views, likes,
          comments_count, read_time, tags, published_at,
          blog_categories (name, color, slug)
        `)
        .eq('is_published', true)
        .eq('is_featured', true)
        .order('published_at', { ascending: false })
        .limit(3);

      if (error) throw error;
      setFeaturedPosts(data || []);
    } catch (error) {
      console.error('Error fetching featured posts:', error);
    }
  };

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      
      let query = supabase
        .from('blog_articles')
        .select(`
          id, title, slug, excerpt, featured_image, is_featured, views,
          likes, comments_count, read_time, tags, published_at,
          blog_categories (name, color, slug)
        `, { count: 'exact' })
        .eq('is_published', true);

      // Apply filters
      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,excerpt.ilike.%${searchTerm}%`);
      }

      if (selectedCategory !== 'all') {
        query = query.eq('blog_categories.slug', selectedCategory);
      }

      // Pagination
      const limit = 9;
      const from = (currentPage - 1) * limit;
      const to = from + limit - 1;

      const { data, error, count } = await query
        .order('published_at', { ascending: false })
        .range(from, to);

      if (error) throw error;
      
      setPosts(data || []);
      setTotalPages(Math.ceil((count || 0) / limit));
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Tennis Whisperer Blog
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Expert insights, training tips, and the latest in tennis equipment and techniques
          </p>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <div className="mb-12">
            <div className="flex items-center gap-2 mb-6">
              <TrendingUp className="h-6 w-6 text-primary" />
              <h2 className="text-2xl font-bold text-foreground">Featured Articles</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                  <div className="relative">
                    <img
                      src={post.featured_image || '/api/placeholder/400/200'}
                      alt={post.title}
                      className="w-full h-48 object-cover rounded-t-2xl"
                    />
                    <Badge className="absolute top-3 left-3 bg-primary/20 text-primary border-primary/30">
                      Featured
                    </Badge>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                          {post.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                          {post.excerpt}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {formatNumber(post.views)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {post.likes}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {post.read_time} min
                        </div>
                      </div>
                      
                      <Button asChild variant="outline" className="w-full glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]">
                        <Link href={`/blog/${post.slug}`}>
                          Read More
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="glass-effect border border-white/10 neo-shadow mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search articles..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-effect border-white/20 text-foreground"
                  />
                </div>
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory('all')}
                  className="min-h-[44px]"
                >
                  All
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.slug ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory(category.slug)}
                    className="min-h-[44px]"
                    style={{
                      borderColor: selectedCategory === category.slug ? category.color : undefined,
                      backgroundColor: selectedCategory === category.slug ? category.color + '20' : undefined,
                      color: selectedCategory === category.slug ? category.color : undefined
                    }}
                  >
                    {category.name} ({category.post_count})
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Blog Posts Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(9)].map((_, i) => (
              <Card key={i} className="glass-effect border border-white/10 neo-shadow animate-pulse">
                <div className="h-48 bg-white/20 rounded-t-2xl"></div>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-white/20 rounded"></div>
                    <div className="h-3 bg-white/10 rounded w-3/4"></div>
                    <div className="h-8 bg-white/10 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {posts.map((post) => (
                <Card key={post.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                  <div className="relative">
                    <img
                      src={post.featured_image || '/api/placeholder/400/200'}
                      alt={post.title}
                      className="w-full h-48 object-cover rounded-t-2xl"
                    />
                    {post.blog_categories && (
                      <Badge
                        className="absolute top-3 left-3"
                        style={{
                          backgroundColor: (Array.isArray(post.blog_categories)
                            ? post.blog_categories[0]?.color
                            : post.blog_categories.color) + '20',
                          color: Array.isArray(post.blog_categories)
                            ? post.blog_categories[0]?.color
                            : post.blog_categories.color,
                          borderColor: (Array.isArray(post.blog_categories)
                            ? post.blog_categories[0]?.color
                            : post.blog_categories.color) + '50'
                        }}
                      >
                        {Array.isArray(post.blog_categories)
                          ? post.blog_categories[0]?.name
                          : post.blog_categories.name}
                      </Badge>
                    )}
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                          {post.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                          {post.excerpt}
                        </p>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          Tennis Whisperer
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(post.published_at)}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {formatNumber(post.views)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {post.likes}
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {post.comments_count}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {post.read_time} min
                        </div>
                      </div>
                      
                      <Button asChild variant="outline" className="w-full glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]">
                        <Link href={`/blog/${post.slug}`}>
                          Read Article
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                >
                  Previous
                </Button>
                
                {[...Array(totalPages)].map((_, i) => (
                  <Button
                    key={i + 1}
                    variant={currentPage === i + 1 ? 'default' : 'outline'}
                    onClick={() => setCurrentPage(i + 1)}
                    className="min-h-[44px] min-w-[44px]"
                  >
                    {i + 1}
                  </Button>
                ))}
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                >
                  Next
                </Button>
              </div>
            )}
          </>
        ) : (
          <Card className="glass-effect border border-white/10 neo-shadow">
            <CardContent className="p-12 text-center">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No articles found</h3>
              <p className="text-muted-foreground">
                {searchTerm || selectedCategory !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Check back soon for new tennis insights and tips'
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
