import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { data: article, error } = await supabase
      .from('blog_articles')
      .select(`
        *,
        blog_categories (name, color)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching article:', error);
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    // Fetch author information separately
    let authorInfo = { full_name: 'Unknown', email: '' };
    if (article?.author_id) {
      const { data: userData } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', article.author_id)
        .single();

      if (userData) {
        authorInfo = userData;
      }
    }

    const articleWithAuthor = {
      ...article,
      users: authorInfo
    };

    return NextResponse.json({ article: articleWithAuthor });

  } catch (error) {
    console.error('Error in blog article GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      excerpt,
      content,
      category_id,
      featured_image,
      is_featured,
      is_published,
      seo_title,
      seo_description,
      tags
    } = body;

    // Validate required fields
    if (!title || !excerpt || !content) {
      return NextResponse.json({ 
        error: 'Missing required fields: title, excerpt, content' 
      }, { status: 400 });
    }

    // Generate slug
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Calculate read time
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / wordsPerMinute);

    // Get current article to check if it was previously unpublished
    const { data: currentArticle } = await supabase
      .from('blog_articles')
      .select('is_published')
      .eq('id', params.id)
      .single();

    // Prepare update data
    const updateData: any = {
      title,
      slug,
      excerpt,
      content,
      category_id: category_id || null,
      featured_image: featured_image || null,
      is_featured: is_featured || false,
      is_published: is_published || false,
      seo_title: seo_title || null,
      seo_description: seo_description || null,
      tags: tags || [],
      read_time: readTime,
      updated_at: new Date().toISOString(),
    };

    // If publishing for the first time, set published_at
    if (is_published && !currentArticle?.is_published) {
      updateData.published_at = new Date().toISOString();
    }

    const { data: article, error } = await supabase
      .from('blog_articles')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        blog_categories (name, color)
      `)
      .single();

    if (error) {
      console.error('Error updating article:', error);
      return NextResponse.json({ error: 'Failed to update article' }, { status: 500 });
    }

    // Fetch author information separately
    let authorInfo = { full_name: 'Unknown', email: '' };
    if (article?.author_id) {
      const { data: userData } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', article.author_id)
        .single();

      if (userData) {
        authorInfo = userData;
      }
    }

    const articleWithAuthor = {
      ...article,
      users: authorInfo
    };

    return NextResponse.json({ article: articleWithAuthor });

  } catch (error) {
    console.error('Error in blog article PUT API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || userData?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { error } = await supabase
      .from('blog_articles')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting article:', error);
      return NextResponse.json({ error: 'Failed to delete article' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Article deleted successfully' });

  } catch (error) {
    console.error('Error in blog article DELETE API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
