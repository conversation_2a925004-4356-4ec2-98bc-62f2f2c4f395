"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON>Footer, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useCreateCategory, useUpdateCategory, useCategories, Category } from "@/hooks/useCategories";

const categorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(100, "Category name too long"),
  description: z.string().optional(),
  image: z.string().url("Invalid URL").optional().or(z.literal("")),
  parent_id: z.string().uuid().optional().nullable(),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category?: Category | null;
  onSuccess?: () => void;
}

export function CategoryDialog({ open, onOpenChange, category, onSuccess }: CategoryDialogProps) {
  const { toast } = useToast();
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const isEditing = !!category;

  // Fetch parent categories (excluding current category if editing)
  const { data: parentCategories = [] } = useCategories({
    include_inactive: false
  });

  const availableParents = parentCategories.filter(parent => 
    !isEditing || parent.id !== category?.id
  );

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      image: "",
      parent_id: null,
      sort_order: 0,
      is_active: true,
    }
  });

  const isActive = watch("is_active");
  const parentId = watch("parent_id");

  // Reset form when dialog opens/closes or category changes
  useEffect(() => {
    if (open) {
      if (category) {
        reset({
          name: category.name,
          description: category.description || "",
          image: category.image || "",
          parent_id: category.parent_id || null,
          sort_order: category.sort_order || 0,
          is_active: category.is_active,
        });
      } else {
        reset({
          name: "",
          description: "",
          image: "",
          parent_id: null,
          sort_order: 0,
          is_active: true,
        });
      }
    }
  }, [open, category, reset]);

  const onSubmit = async (data: CategoryFormData) => {
    try {
      // Convert empty strings to undefined for optional fields
      const cleanData = {
        ...data,
        description: data.description || undefined,
        image: data.image || undefined,
        parent_id: data.parent_id || undefined,
        sort_order: data.sort_order || 0,
      };

      if (isEditing && category) {
        await updateCategoryMutation.mutateAsync({
          id: category.id,
          ...cleanData,
        });
        toast({
          title: "Success",
          description: "Category updated successfully",
        });
      } else {
        await createCategoryMutation.mutateAsync(cleanData);
        toast({
          title: "Success",
          description: "Category created successfully",
        });
      }

      onSuccess?.();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEditing ? "update" : "create"} category`,
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] neo-shadow">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Category" : "Create New Category"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update the category information below." 
              : "Add a new category to organize your products."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Category Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Category Name *</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="e.g., Tennis Rackets, Shoes"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            {/* Parent Category */}
            <div className="space-y-2">
              <Label htmlFor="parent_id">Parent Category</Label>
              <Select
                value={parentId || "none"}
                onValueChange={(value) => setValue("parent_id", value === "none" ? null : value)}
              >
                <SelectTrigger className="min-h-[44px] neo-shadow-inset">
                  <SelectValue placeholder="Select parent category (optional)" />
                </SelectTrigger>
                <SelectContent className="neo-shadow">
                  <SelectItem value="none">No Parent (Root Category)</SelectItem>
                  {availableParents.map((parent) => (
                    <SelectItem key={parent.id} value={parent.id}>
                      {parent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Brief description of the category..."
              rows={3}
              className="neo-shadow-inset"
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Image URL */}
            <div className="space-y-2">
              <Label htmlFor="image">Image URL</Label>
              <Input
                id="image"
                {...register("image")}
                placeholder="https://example.com/image.jpg"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.image && (
                <p className="text-sm text-destructive">{errors.image.message}</p>
              )}
            </div>

            {/* Sort Order */}
            <div className="space-y-2">
              <Label htmlFor="sort_order">Sort Order</Label>
              <Input
                id="sort_order"
                type="number"
                {...register("sort_order", { 
                  setValueAs: (value) => value === "" ? 0 : parseInt(value) 
                })}
                placeholder="0"
                min="0"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.sort_order && (
                <p className="text-sm text-destructive">{errors.sort_order.message}</p>
              )}
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={isActive}
              onCheckedChange={(checked) => setValue("is_active", checked)}
              className="neo-shadow"
            />
            <Label htmlFor="is_active">
              Active Category
              <span className="block text-sm text-muted-foreground">
                Inactive categories won't appear in product forms
              </span>
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="min-h-[44px] neo-shadow hover:neo-shadow-light transition-neo"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || createCategoryMutation.isPending || updateCategoryMutation.isPending}
              className="min-h-[44px] neo-shadow hover:neo-shadow-light transition-neo"
            >
              {isSubmitting || createCategoryMutation.isPending || updateCategoryMutation.isPending
                ? (isEditing ? "Updating..." : "Creating...")
                : (isEditing ? "Update Category" : "Create Category")
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
