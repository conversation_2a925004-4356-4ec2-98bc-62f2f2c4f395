"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Award, Plus, Search, Edit, Trash2, ExternalLink, Globe } from "lucide-react";
import { useBrands, useDeleteBrand } from "@/hooks/useBrands";
import { useToast } from "@/hooks/use-toast";
import { BrandDialog } from "@/components/admin/brand-dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

export default function BrandsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingBrand, setEditingBrand] = useState<any>(null);
  const { toast } = useToast();

  // Fetch brands with search filter
  const { data: brands = [], isLoading, error } = useBrands({
    search: searchTerm || undefined,
    include_inactive: true
  });

  const deleteBrandMutation = useDeleteBrand();

  const handleDeleteBrand = async (brandId: string, brandName: string) => {
    try {
      await deleteBrandMutation.mutateAsync(brandId);
      toast({
        title: "Success",
        description: `Brand "${brandName}" deleted successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete brand",
        variant: "destructive",
      });
    }
  };

  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    brand.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    brand.country?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Brands</h1>
        </div>
        <Card className="neo-shadow border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-orange-800 mb-2">
                Database Migration Required
              </h3>
              <p className="text-orange-700 mb-4">
                The brand management system requires a database migration to create the brands table.
              </p>
              <div className="bg-orange-100 p-4 rounded-lg text-left">
                <p className="text-sm text-orange-800 font-medium mb-2">
                  To enable brand management:
                </p>
                <ol className="text-sm text-orange-700 space-y-1 list-decimal list-inside">
                  <li>Open your Supabase dashboard</li>
                  <li>Go to SQL Editor</li>
                  <li>Run the migration script: <code className="bg-orange-200 px-1 rounded">DATABASE_MIGRATION_CATEGORIES_BRANDS.sql</code></li>
                  <li>Refresh this page</li>
                </ol>
              </div>
              <p className="text-xs text-orange-600 mt-3">
                Error: {error.message}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Brands</h1>
        <Button 
          onClick={() => setIsCreateDialogOpen(true)}
          className="min-h-[44px] neo-shadow hover:neo-shadow-light transition-neo"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Brand
        </Button>
      </div>

      <Card className="neo-shadow">
        <CardHeader>
          <CardTitle>Brand Management</CardTitle>
          <CardDescription>
            Manage your product brands to organize your inventory by manufacturer.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search brands..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 min-h-[44px] neo-shadow-inset"
              />
            </div>
          </div>

          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-muted animate-pulse rounded-lg neo-shadow-inset" />
              ))}
            </div>
          ) : brands.length === 0 && !isLoading ? (
            <div className="text-center py-12">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 neo-shadow">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  No Brands Available
                </h3>
                <p className="text-blue-700 mb-4">
                  The brands table may not exist yet. Run the database migration to enable brand management.
                </p>
                <div className="bg-blue-100 p-4 rounded-lg text-left">
                  <p className="text-sm text-blue-800 font-medium mb-2">
                    To enable brand management:
                  </p>
                  <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                    <li>Open your Supabase dashboard</li>
                    <li>Go to SQL Editor</li>
                    <li>Run the migration script: <code className="bg-blue-200 px-1 rounded">DATABASE_MIGRATION_CATEGORIES_BRANDS.sql</code></li>
                    <li>Refresh this page</li>
                  </ol>
                </div>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left font-medium py-3 px-4">Brand</th>
                    <th className="text-left font-medium py-3 px-4">Country</th>
                    <th className="text-left font-medium py-3 px-4">Founded</th>
                    <th className="text-right font-medium py-3 px-4">Products</th>
                    <th className="text-center font-medium py-3 px-4">Status</th>
                    <th className="text-right font-medium py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBrands.map((brand) => (
                    <tr key={brand.id} className="border-b border-border hover:bg-muted/50 transition-colors">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-lg overflow-hidden bg-muted flex items-center justify-center neo-shadow-inset">
                            {brand.logo ? (
                              <img
                                src={brand.logo}
                                alt={brand.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <Award className="h-5 w-5 text-muted-foreground" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{brand.name}</p>
                            {brand.description && (
                              <p className="text-sm text-muted-foreground line-clamp-1">
                                {brand.description}
                              </p>
                            )}
                            {brand.website && (
                              <a
                                href={brand.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-primary hover:underline flex items-center gap-1 mt-1"
                              >
                                <Globe className="h-3 w-3" />
                                Website
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {brand.country && (
                          <Badge variant="outline" className="neo-shadow-light">
                            {brand.country}
                          </Badge>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        {brand.founded_year && (
                          <span className="text-muted-foreground">{brand.founded_year}</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <Badge variant="secondary" className="neo-shadow-light">
                          {brand.product_count}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <Badge 
                          variant={brand.is_active ? "default" : "secondary"}
                          className="neo-shadow-light"
                        >
                          {brand.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-10 w-10 neo-shadow hover:neo-shadow-light transition-neo"
                            onClick={() => setEditingBrand(brand)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-10 w-10 text-destructive neo-shadow hover:neo-shadow-light transition-neo"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="neo-shadow">
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Brand</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{brand.name}"? This action cannot be undone.
                                  {brand.product_count > 0 && (
                                    <span className="block mt-2 text-destructive font-medium">
                                      Warning: This brand has {brand.product_count} product(s) associated with it.
                                    </span>
                                  )}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel className="neo-shadow hover:neo-shadow-light transition-neo">
                                  Cancel
                                </AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteBrand(brand.id, brand.name)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90 neo-shadow hover:neo-shadow-light transition-neo"
                                  disabled={deleteBrandMutation.isPending}
                                >
                                  {deleteBrandMutation.isPending ? "Deleting..." : "Delete"}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </td>
                    </tr>
                  ))}
                  {filteredBrands.length === 0 && !isLoading && (
                    <tr>
                      <td colSpan={6} className="py-6 text-center text-muted-foreground">
                        {searchTerm ? "No brands found matching your search." : "No brands found. Add your first brand to get started."}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Brand Dialog */}
      <BrandDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={() => setIsCreateDialogOpen(false)}
      />

      {/* Edit Brand Dialog */}
      <BrandDialog
        open={!!editingBrand}
        onOpenChange={(open) => !open && setEditingBrand(null)}
        brand={editingBrand}
        onSuccess={() => setEditingBrand(null)}
      />
    </div>
  );
}
