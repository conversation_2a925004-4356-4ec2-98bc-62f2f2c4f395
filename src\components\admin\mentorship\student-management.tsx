"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/utils/supabase/client";
import {
  Search,
  Plus,
  MessageCircle,
  Calendar,
  User,
  Mail,
  Phone,
  MapPin,
  GraduationCap,
  Clock,
  TrendingUp,
  Edit,
  Trash2,
  Eye
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface Student {
  id: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  created_at: string;
  role: string;
  enrollments?: StudentEnrollment[];
}

interface StudentEnrollment {
  id: string;
  program_id: string;
  mentor_id: string;
  start_date: string;
  end_date: string;
  status: string;
  payment_type: string;
  program?: {
    name: string;
    duration_months: number;
  };
  mentor?: {
    user_id: string;
    users?: {
      full_name: string | null;
      email: string;
    };
  };
}

interface MentorshipProgram {
  id: string;
  name: string;
  description: string | null;
  duration_months: number;
  price_monthly: number;
  price_upfront: number | null;
}

interface Mentor {
  id: string;
  user_id: string;
  bio: string | null;
  specialties: string[] | null;
  experience_years: number | null;
  users?: {
    full_name: string | null;
    email: string;
  };
}

export function StudentManagement() {
  const [searchQuery, setSearchQuery] = useState("");
  const [students, setStudents] = useState<Student[]>([]);
  const [programs, setPrograms] = useState<MentorshipProgram[]>([]);
  const [mentors, setMentors] = useState<Mentor[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEnrollDialogOpen, setIsEnrollDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [enrollmentForm, setEnrollmentForm] = useState({
    student_id: "",
    program_id: "",
    mentor_id: "",
    payment_type: "monthly" as "monthly" | "upfront",
    start_date: "",
    notes: ""
  });
  const [newStudentForm, setNewStudentForm] = useState({
    email: "",
    full_name: "",
    phone: "",
    password: ""
  });

  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    fetchStudents();
    fetchPrograms();
    fetchMentors();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);

      // Fetch all users with student role and their enrollments
      const { data: studentsData, error } = await supabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          phone,
          created_at,
          role,
          student_enrollments (
            id,
            program_id,
            mentor_id,
            start_date,
            end_date,
            status,
            payment_type,
            mentorship_programs (
              name,
              duration_months
            ),
            mentors (
              user_id,
              users (
                full_name,
                email
              )
            )
          )
        `)
        .eq('role', 'student')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching students:', error);
        toast({
          title: "Error",
          description: "Failed to fetch students",
          variant: "destructive",
        });
        return;
      }

      setStudents(studentsData || []);
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPrograms = async () => {
    try {
      const { data, error } = await supabase
        .from('mentorship_programs')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching programs:', error);
        return;
      }

      setPrograms(data || []);
    } catch (error) {
      console.error('Error fetching programs:', error);
    }
  };

  const fetchMentors = async () => {
    try {
      const { data, error } = await supabase
        .from('mentors')
        .select(`
          id,
          user_id,
          bio,
          specialties,
          experience_years,
          users (
            full_name,
            email
          )
        `)
        .order('created_at');

      if (error) {
        console.error('Error fetching mentors:', error);
        return;
      }

      setMentors(data || []);
    } catch (error) {
      console.error('Error fetching mentors:', error);
    }
  };

  const handleAddStudent = async () => {
    if (!newStudentForm.email || !newStudentForm.full_name || !newStudentForm.password) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      // Create user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newStudentForm.email,
        password: newStudentForm.password,
        options: {
          data: {
            full_name: newStudentForm.full_name,
            phone: newStudentForm.phone,
            role: 'student'
          }
        }
      });

      if (authError) {
        console.error('Auth error:', authError);
        toast({
          title: "Error",
          description: authError.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: "Student account created successfully",
      });

      setIsAddDialogOpen(false);
      setNewStudentForm({
        email: "",
        full_name: "",
        phone: "",
        password: ""
      });

      // Refresh students list
      fetchStudents();
    } catch (error) {
      console.error('Error creating student:', error);
      toast({
        title: "Error",
        description: "Failed to create student account",
        variant: "destructive",
      });
    }
  };

  const handleEnrollStudent = async () => {
    if (!enrollmentForm.student_id || !enrollmentForm.program_id || !enrollmentForm.mentor_id || !enrollmentForm.start_date) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const selectedProgram = programs.find(p => p.id === enrollmentForm.program_id);
      if (!selectedProgram) return;

      // Calculate end date based on program duration
      const startDate = new Date(enrollmentForm.start_date);
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + selectedProgram.duration_months);

      const { error } = await supabase
        .from('student_enrollments')
        .insert({
          student_id: enrollmentForm.student_id,
          program_id: enrollmentForm.program_id,
          mentor_id: enrollmentForm.mentor_id,
          start_date: enrollmentForm.start_date,
          end_date: endDate.toISOString().split('T')[0],
          payment_type: enrollmentForm.payment_type,
          status: 'active'
        });

      if (error) {
        console.error('Error enrolling student:', error);
        toast({
          title: "Error",
          description: "Failed to enroll student",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: "Student enrolled successfully",
      });

      setIsEnrollDialogOpen(false);
      setEnrollmentForm({
        student_id: "",
        program_id: "",
        mentor_id: "",
        payment_type: "monthly",
        start_date: "",
        notes: ""
      });

      // Refresh students list
      fetchStudents();
    } catch (error) {
      console.error('Error enrolling student:', error);
      toast({
        title: "Error",
        description: "Failed to enroll student",
        variant: "destructive",
      });
    }
  };

  const filteredStudents = students.filter(student =>
    student.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-100 text-green-800 border-green-200",
      completed: "bg-blue-100 text-blue-800 border-blue-200",
      cancelled: "bg-red-100 text-red-800 border-red-200"
    };

    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{students.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Active Enrollments</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {students.reduce((acc, student) =>
                acc + (student.enrollments?.filter(e => e.status === 'active').length || 0), 0
              )}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">New This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {students.filter(student => {
                const createdDate = new Date(student.created_at);
                const now = new Date();
                return createdDate.getMonth() === now.getMonth() &&
                       createdDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Programs Available</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{programs.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-96">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search students..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          <div className="flex gap-2">
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Student
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Student</DialogTitle>
                  <DialogDescription>
                    Create a new student account and profile
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newStudentForm.email}
                      onChange={(e) => setNewStudentForm(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="full_name">Full Name *</Label>
                    <Input
                      id="full_name"
                      placeholder="John Doe"
                      value={newStudentForm.full_name}
                      onChange={(e) => setNewStudentForm(prev => ({ ...prev, full_name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      placeholder="+****************"
                      value={newStudentForm.phone}
                      onChange={(e) => setNewStudentForm(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">Temporary Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Temporary password"
                      value={newStudentForm.password}
                      onChange={(e) => setNewStudentForm(prev => ({ ...prev, password: e.target.value }))}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddStudent}>
                    Create Student
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isEnrollDialogOpen} onOpenChange={setIsEnrollDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Enroll Student
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Enroll Student in Program</DialogTitle>
                  <DialogDescription>
                    Assign a student to a mentorship program
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="student">Student *</Label>
                    <Select
                      value={enrollmentForm.student_id}
                      onValueChange={(value) => setEnrollmentForm(prev => ({ ...prev, student_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select student" />
                      </SelectTrigger>
                      <SelectContent>
                        {students.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            {student.full_name || student.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="program">Program *</Label>
                    <Select
                      value={enrollmentForm.program_id}
                      onValueChange={(value) => setEnrollmentForm(prev => ({ ...prev, program_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select program" />
                      </SelectTrigger>
                      <SelectContent>
                        {programs.map((program) => (
                          <SelectItem key={program.id} value={program.id}>
                            {program.name} ({program.duration_months} months)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="mentor">Mentor *</Label>
                    <Select
                      value={enrollmentForm.mentor_id}
                      onValueChange={(value) => setEnrollmentForm(prev => ({ ...prev, mentor_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select mentor" />
                      </SelectTrigger>
                      <SelectContent>
                        {mentors.map((mentor) => (
                          <SelectItem key={mentor.id} value={mentor.id}>
                            {mentor.users?.full_name || mentor.users?.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="payment_type">Payment Type *</Label>
                    <Select
                      value={enrollmentForm.payment_type}
                      onValueChange={(value: "monthly" | "upfront") => setEnrollmentForm(prev => ({ ...prev, payment_type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="upfront">Upfront</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={enrollmentForm.start_date}
                      onChange={(e) => setEnrollmentForm(prev => ({ ...prev, start_date: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Additional notes..."
                      value={enrollmentForm.notes}
                      onChange={(e) => setEnrollmentForm(prev => ({ ...prev, notes: e.target.value }))}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsEnrollDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleEnrollStudent}>
                    Enroll Student
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Students Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Enrollments</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    {searchQuery ? "No students found matching your search." : "No students found. Add your first student to get started."}
                  </TableCell>
                </TableRow>
              ) : (
                filteredStudents.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {student.full_name || "No name provided"}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ID: {student.id.slice(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1 text-muted-foreground" />
                          {student.email}
                        </div>
                        {student.phone && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Phone className="h-3 w-3 mr-1" />
                            {student.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {student.enrollments && student.enrollments.length > 0 ? (
                          student.enrollments.map((enrollment) => (
                            <div key={enrollment.id} className="text-sm">
                              <div className="font-medium">
                                {enrollment.program?.name || "Unknown Program"}
                              </div>
                              <div className="text-muted-foreground">
                                {enrollment.mentor?.users?.full_name || "No mentor assigned"}
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-sm text-muted-foreground">
                            No active enrollments
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {student.enrollments && student.enrollments.length > 0 ? (
                          student.enrollments.map((enrollment) => (
                            <div key={enrollment.id}>
                              {getStatusBadge(enrollment.status)}
                            </div>
                          ))
                        ) : (
                          <Badge variant="outline">Not Enrolled</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(student.created_at)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // TODO: Open student profile dialog
                            toast({
                              title: "Feature Coming Soon",
                              description: "Student profile view will be available soon",
                            });
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // TODO: Open communication dialog
                            toast({
                              title: "Feature Coming Soon",
                              description: "Direct messaging will be available soon",
                            });
                          }}
                        >
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // TODO: Open session scheduling dialog
                            toast({
                              title: "Feature Coming Soon",
                              description: "Session scheduling will be available soon",
                            });
                          }}
                        >
                          <Calendar className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedStudent(student);
                            setEnrollmentForm(prev => ({ ...prev, student_id: student.id }));
                            setIsEnrollDialogOpen(true);
                          }}
                        >
                          <GraduationCap className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
    </Card>
  );
}
