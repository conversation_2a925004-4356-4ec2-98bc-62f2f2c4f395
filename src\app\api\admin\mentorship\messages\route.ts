import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('student_id');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!studentId) {
      return NextResponse.json({ error: 'student_id parameter is required' }, { status: 400 });
    }

    // Fetch messages between admin and student
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        id,
        sender_id,
        recipient_id,
        content,
        read,
        created_at,
        sender:users!messages_sender_id_fkey(full_name, email),
        recipient:users!messages_recipient_id_fkey(full_name, email)
      `)
      .or(`and(sender_id.eq.${studentId},recipient_id.eq.${user.id}),and(sender_id.eq.${user.id},recipient_id.eq.${studentId})`)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    return NextResponse.json({
      messages: messages || [],
      total: messages?.length || 0
    });

  } catch (error) {
    console.error('Error in messages API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { recipient_id, content } = body;

    // Validate required fields
    if (!recipient_id || !content) {
      return NextResponse.json({ 
        error: 'Missing required fields: recipient_id and content are required' 
      }, { status: 400 });
    }

    // Validate content length
    if (content.trim().length === 0) {
      return NextResponse.json({ error: 'Message content cannot be empty' }, { status: 400 });
    }

    if (content.length > 2000) {
      return NextResponse.json({ error: 'Message content too long (max 2000 characters)' }, { status: 400 });
    }

    // Verify recipient exists and is a student (if sender is admin) or admin (if sender is student)
    const { data: recipient, error: recipientError } = await supabase
      .from('users')
      .select('id, role, admin_role')
      .eq('id', recipient_id)
      .single();

    if (recipientError || !recipient) {
      return NextResponse.json({ error: 'Invalid recipient' }, { status: 400 });
    }

    // Get sender info
    const { data: sender, error: senderError } = await supabase
      .from('users')
      .select('id, role, admin_role')
      .eq('id', user.id)
      .single();

    if (senderError || !sender) {
      return NextResponse.json({ error: 'Invalid sender' }, { status: 400 });
    }

    // Validate communication permissions
    const isAdminToStudent = sender.admin_role && recipient.role === 'student';
    const isStudentToAdmin = sender.role === 'student' && recipient.admin_role;

    if (!isAdminToStudent && !isStudentToAdmin) {
      return NextResponse.json({ 
        error: 'Invalid communication: Only admin-student communication is allowed' 
      }, { status: 403 });
    }

    // Create message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        sender_id: user.id,
        recipient_id,
        content: content.trim(),
        read: false
      })
      .select(`
        id,
        sender_id,
        recipient_id,
        content,
        read,
        created_at,
        sender:users!messages_sender_id_fkey(full_name, email),
        recipient:users!messages_recipient_id_fkey(full_name, email)
      `)
      .single();

    if (messageError) {
      console.error('Error creating message:', messageError);
      return NextResponse.json({ 
        error: messageError.message || 'Failed to send message' 
      }, { status: 500 });
    }

    // Log admin activity if sender is admin
    if (sender.admin_role) {
      await supabase
        .from('admin_activity_logs')
        .insert({
          admin_id: user.id,
          action: 'send_message',
          details: {
            message_id: message.id,
            recipient_id,
            content_length: content.length
          },
          success: true
        });
    }

    return NextResponse.json({
      message: 'Message sent successfully',
      data: message
    });

  } catch (error) {
    console.error('Error sending message:', error);
    
    // Log failed admin activity if applicable
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: userData } = await supabase
          .from('users')
          .select('admin_role')
          .eq('id', user.id)
          .single();

        if (userData?.admin_role) {
          await supabase
            .from('admin_activity_logs')
            .insert({
              admin_id: user.id,
              action: 'send_message',
              details: { error: error instanceof Error ? error.message : 'Unknown error' },
              success: false,
              error_message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
      }
    } catch (logError) {
      console.error('Error logging admin activity:', logError);
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { student_id, mark_as_read } = body;

    if (!student_id || mark_as_read === undefined) {
      return NextResponse.json({ 
        error: 'Missing required fields: student_id and mark_as_read are required' 
      }, { status: 400 });
    }

    // Mark messages as read
    const { error: updateError } = await supabase
      .from('messages')
      .update({ read: mark_as_read })
      .eq('sender_id', student_id)
      .eq('recipient_id', user.id);

    if (updateError) {
      console.error('Error updating message read status:', updateError);
      return NextResponse.json({ error: 'Failed to update message status' }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Message status updated successfully'
    });

  } catch (error) {
    console.error('Error updating message status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
