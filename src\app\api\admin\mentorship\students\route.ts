import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('admin_role')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.admin_role) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('users')
      .select(`
        id,
        full_name,
        email,
        phone,
        created_at,
        role,
        student_enrollments (
          id,
          program_id,
          mentor_id,
          start_date,
          end_date,
          status,
          payment_type,
          mentorship_programs (
            name,
            duration_months
          ),
          mentors (
            user_id,
            users (
              full_name,
              email
            )
          )
        )
      `)
      .eq('role', 'student')
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Add search filter if provided
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    const { data: students, error } = await query;

    if (error) {
      console.error('Error fetching students:', error);
      return NextResponse.json({ error: 'Failed to fetch students' }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'student');

    if (search) {
      countQuery = countQuery.or(`full_name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error getting student count:', countError);
    }

    return NextResponse.json({
      students: students || [],
      total: count || 0,
      limit,
      offset
    });

  } catch (error) {
    console.error('Error in students API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('admin_role')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.admin_role) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { email, full_name, phone, password } = body;

    // Validate required fields
    if (!email || !full_name || !password) {
      return NextResponse.json({ 
        error: 'Missing required fields: email, full_name, and password are required' 
      }, { status: 400 });
    }

    // Create user account using admin service role
    const { data: authData, error: authCreateError } = await supabase.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        full_name,
        phone,
        role: 'student'
      },
      email_confirm: true // Auto-confirm email for admin-created accounts
    });

    if (authCreateError) {
      console.error('Error creating user account:', authCreateError);
      return NextResponse.json({ 
        error: authCreateError.message || 'Failed to create user account' 
      }, { status: 400 });
    }

    if (!authData.user) {
      return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
    }

    // Log admin activity
    await supabase
      .from('admin_activity_logs')
      .insert({
        admin_id: user.id,
        action: 'create_student',
        details: {
          student_id: authData.user.id,
          student_email: email,
          student_name: full_name
        },
        success: true
      });

    return NextResponse.json({
      message: 'Student created successfully',
      student: {
        id: authData.user.id,
        email: authData.user.email,
        full_name,
        phone,
        created_at: authData.user.created_at
      }
    });

  } catch (error) {
    console.error('Error creating student:', error);
    
    // Log failed admin activity
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('admin_activity_logs')
          .insert({
            admin_id: user.id,
            action: 'create_student',
            details: { error: error instanceof Error ? error.message : 'Unknown error' },
            success: false,
            error_message: error instanceof Error ? error.message : 'Unknown error'
          });
      }
    } catch (logError) {
      console.error('Error logging admin activity:', logError);
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
