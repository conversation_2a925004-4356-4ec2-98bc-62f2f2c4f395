import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('admin_role')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.admin_role) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('student_id');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('student_enrollments')
      .select(`
        id,
        student_id,
        program_id,
        mentor_id,
        start_date,
        end_date,
        payment_type,
        status,
        created_at,
        updated_at,
        students:users!student_enrollments_student_id_fkey (
          id,
          full_name,
          email
        ),
        mentorship_programs (
          id,
          name,
          description,
          duration_months,
          price_monthly,
          price_upfront
        ),
        mentors (
          id,
          user_id,
          bio,
          specialties,
          experience_years,
          users (
            full_name,
            email
          )
        )
      `)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Add filters if provided
    if (studentId) {
      query = query.eq('student_id', studentId);
    }
    if (status) {
      query = query.eq('status', status);
    }

    const { data: enrollments, error } = await query;

    if (error) {
      console.error('Error fetching enrollments:', error);
      return NextResponse.json({ error: 'Failed to fetch enrollments' }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('student_enrollments')
      .select('*', { count: 'exact', head: true });

    if (studentId) {
      countQuery = countQuery.eq('student_id', studentId);
    }
    if (status) {
      countQuery = countQuery.eq('status', status);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error getting enrollment count:', countError);
    }

    return NextResponse.json({
      enrollments: enrollments || [],
      total: count || 0,
      limit,
      offset
    });

  } catch (error) {
    console.error('Error in enrollments API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated and is an admin
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('admin_role')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.admin_role) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { student_id, program_id, mentor_id, start_date, payment_type, notes } = body;

    // Validate required fields
    if (!student_id || !program_id || !mentor_id || !start_date || !payment_type) {
      return NextResponse.json({ 
        error: 'Missing required fields: student_id, program_id, mentor_id, start_date, and payment_type are required' 
      }, { status: 400 });
    }

    // Get program details to calculate end date
    const { data: program, error: programError } = await supabase
      .from('mentorship_programs')
      .select('duration_months')
      .eq('id', program_id)
      .single();

    if (programError || !program) {
      return NextResponse.json({ error: 'Invalid program ID' }, { status: 400 });
    }

    // Calculate end date
    const startDate = new Date(start_date);
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + program.duration_months);

    // Check if student is already enrolled in an active program
    const { data: existingEnrollment, error: checkError } = await supabase
      .from('student_enrollments')
      .select('id')
      .eq('student_id', student_id)
      .eq('status', 'active')
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing enrollment:', checkError);
      return NextResponse.json({ error: 'Failed to check existing enrollments' }, { status: 500 });
    }

    if (existingEnrollment) {
      return NextResponse.json({ 
        error: 'Student is already enrolled in an active program' 
      }, { status: 400 });
    }

    // Create enrollment
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .insert({
        student_id,
        program_id,
        mentor_id,
        start_date,
        end_date: endDate.toISOString().split('T')[0],
        payment_type,
        status: 'active'
      })
      .select(`
        id,
        student_id,
        program_id,
        mentor_id,
        start_date,
        end_date,
        payment_type,
        status,
        created_at,
        students:users!student_enrollments_student_id_fkey (
          full_name,
          email
        ),
        mentorship_programs (
          name,
          duration_months
        ),
        mentors (
          users (
            full_name,
            email
          )
        )
      `)
      .single();

    if (enrollmentError) {
      console.error('Error creating enrollment:', enrollmentError);
      return NextResponse.json({ 
        error: enrollmentError.message || 'Failed to create enrollment' 
      }, { status: 500 });
    }

    // Log admin activity
    await supabase
      .from('admin_activity_logs')
      .insert({
        admin_id: user.id,
        action: 'create_enrollment',
        details: {
          enrollment_id: enrollment.id,
          student_id,
          program_id,
          mentor_id,
          notes
        },
        success: true
      });

    return NextResponse.json({
      message: 'Student enrolled successfully',
      enrollment
    });

  } catch (error) {
    console.error('Error creating enrollment:', error);
    
    // Log failed admin activity
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('admin_activity_logs')
          .insert({
            admin_id: user.id,
            action: 'create_enrollment',
            details: { error: error instanceof Error ? error.message : 'Unknown error' },
            success: false,
            error_message: error instanceof Error ? error.message : 'Unknown error'
          });
      }
    } catch (logError) {
      console.error('Error logging admin activity:', logError);
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
