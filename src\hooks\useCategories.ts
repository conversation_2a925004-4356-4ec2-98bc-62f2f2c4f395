import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '../utils/supabase/client';

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent_id?: string;
  sort_order: number;
  is_active: boolean;
  product_count: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  parent?: { name: string };
  children?: Array<{
    id: string;
    name: string;
    slug: string;
    product_count: number;
  }>;
}

export interface CategoryFilters {
  include_inactive?: boolean;
  parent_id?: string | null;
  search?: string;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  image?: string;
  parent_id?: string;
  sort_order?: number;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string;
}

// Hook to fetch all categories with optional filters
export const useCategories = (filters?: CategoryFilters) => {
  const queryParams = new URLSearchParams();

  if (filters?.include_inactive) {
    queryParams.set('include_inactive', 'true');
  }
  if (filters?.parent_id !== undefined) {
    queryParams.set('parent_id', filters.parent_id || 'null');
  }
  if (filters?.search) {
    queryParams.set('search', filters.search);
  }

  return useQuery({
    queryKey: ['categories', filters],
    queryFn: async (): Promise<Category[]> => {
      const url = `/api/categories${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to fetch a single category by ID
export const useCategory = (id: string) => {
  return useQuery({
    queryKey: ['categories', id],
    queryFn: async (): Promise<Category> => {
      const response = await fetch(`/api/categories/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch category');
      }

      return response.json();
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Hook to create a new category (admin only)
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (categoryData: CreateCategoryData): Promise<Category> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create category');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

// Hook to update a category (admin only)
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateCategoryData): Promise<Category> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/categories/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update category');
      }

      return response.json();
    },
    onSuccess: (data) => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      // Update the specific category in cache
      queryClient.setQueryData(['categories', data.id], data);
    },
  });
};

// Hook to delete a category (admin only)
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/categories/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete category');
      }
    },
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      // Remove the specific category from cache
      queryClient.removeQueries({ queryKey: ['categories', deletedId] });
    },
  });
};
