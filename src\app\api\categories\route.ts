import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';
import { createServiceClient } from '../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for category data
const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name too long'),
  slug: z.string().optional(),
  description: z.string().optional(),
  image: z.string().url().optional().or(z.literal('')),
  parent_id: z.string().uuid().optional().nullable(),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('include_inactive') === 'true';
    const parentId = searchParams.get('parent_id');
    const search = searchParams.get('search');

    // Use service client for public category data
    const supabase = createServiceClient();

    // Use the enhanced schema that now exists
    let query = supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        image,
        parent_id,
        sort_order,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `);

    // Filter by active status unless specifically requesting inactive
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    // Filter by parent category
    if (parentId) {
      if (parentId === 'null') {
        query = query.is('parent_id', null);
      } else {
        query = query.eq('parent_id', parentId);
      }
    }

    // Search functionality
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Order by sort_order, then by name
    query = query.order('sort_order', { ascending: true })
                 .order('name', { ascending: true });

    const { data: categories, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      );
    }

    return NextResponse.json(categories || []);
  } catch (error: any) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const body = await request.json();

    // Validate input data
    const validationResult = categorySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { name, description, image, parent_id, sort_order, is_active, metadata } = validationResult.data;

    // Generate slug if not provided
    const slug = validationResult.data.slug || generateSlug(name);

    // Check if slug already exists
    const { data: existingCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category with this name already exists' },
        { status: 409 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();
    const { data: category, error } = await serviceSupabase
      .from('categories')
      .insert({
        name,
        slug,
        description,
        image,
        parent_id,
        sort_order: sort_order || 0,
        is_active: is_active !== undefined ? is_active : true,
        metadata: metadata || {},
        product_count: 0
      })
      .select(`
        id,
        name,
        slug,
        description,
        image,
        parent_id,
        sort_order,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .single();

    if (error) {
      console.error('Error creating category:', error);
      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      );
    }

    return NextResponse.json(category, { status: 201 });
  } catch (error: any) {
    console.error('Error in categories POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
