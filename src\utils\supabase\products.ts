import { createClient } from "./server";
import { v4 as uuidv4 } from "uuid";

export type Product = {
  id: string;
  name: string;
  price: number;
  description?: string;
  image?: string;
  images?: string[]; // Array of product images
  category: string; // Keep for backward compatibility
  category_id?: string; // Foreign key to categories table
  brand_id?: string; // Foreign key to brands table
  stock: number;
  status: string;
  features?: string[]; // Array of product features
  rating?: number; // Product rating (1-5)
  reviews?: number; // Number of reviews
};

export type Category = {
  id: string;
  name: string;
  image?: string;
  count: number;
};

export async function getProducts() {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("products")
    .select("*")
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching products:", error);
    return [];
  }

  return data as Product[];
}

export async function getProduct(id: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("products")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    console.error(`Error fetching product ${id}:`, error);
    return null;
  }

  return data as Product;
}

export async function createProduct(product: Omit<Product, "id">) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("products")
    .insert([{ ...product, id: uuidv4() }])
    .select()
    .single();

  if (error) {
    console.error("Error creating product:", error);
    throw error;
  }

  return data as Product;
}

export async function updateProduct(id: string, updates: Partial<Product>) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("products")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating product ${id}:`, error);
    throw error;
  }

  return data as Product;
}

export async function deleteProduct(id: string) {
  const supabase = await createClient();
  const { error } = await supabase
    .from("products")
    .delete()
    .eq("id", id);

  if (error) {
    console.error(`Error deleting product ${id}:`, error);
    throw error;
  }

  return true;
}

export async function getCategories() {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching categories:", error);
    return [];
  }

  return data as Category[];
}

export async function createCategory(category: Omit<Category, "id">) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("categories")
    .insert([{ ...category, id: uuidv4() }])
    .select()
    .single();

  if (error) {
    console.error("Error creating category:", error);
    throw error;
  }

  return data as Category;
}

export async function updateCategory(id: string, updates: Partial<Category>) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("categories")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating category ${id}:`, error);
    throw error;
  }

  return data as Category;
}

export async function deleteCategory(id: string) {
  const supabase = await createClient();
  const { error } = await supabase
    .from("categories")
    .delete()
    .eq("id", id);

  if (error) {
    console.error(`Error deleting category ${id}:`, error);
    throw error;
  }

  return true;
}