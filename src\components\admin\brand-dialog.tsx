"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useCreateBrand, useUpdateBrand, Brand } from "@/hooks/useBrands";

const brandSchema = z.object({
  name: z.string().min(1, "Brand name is required").max(100, "Brand name too long"),
  description: z.string().optional(),
  logo: z.string().url("Invalid URL").optional().or(z.literal("")),
  website: z.string().url("Invalid URL").optional().or(z.literal("")),
  country: z.string().max(100).optional(),
  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  is_active: z.boolean().optional(),
});

type BrandFormData = z.infer<typeof brandSchema>;

interface BrandDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  brand?: Brand | null;
  onSuccess?: () => void;
}

export function BrandDialog({ open, onOpenChange, brand, onSuccess }: BrandDialogProps) {
  const { toast } = useToast();
  const createBrandMutation = useCreateBrand();
  const updateBrandMutation = useUpdateBrand();
  const isEditing = !!brand;

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<BrandFormData>({
    resolver: zodResolver(brandSchema),
    defaultValues: {
      name: "",
      description: "",
      logo: "",
      website: "",
      country: "",
      founded_year: undefined,
      is_active: true,
    }
  });

  const isActive = watch("is_active");

  // Reset form when dialog opens/closes or brand changes
  useEffect(() => {
    if (open) {
      if (brand) {
        reset({
          name: brand.name,
          description: brand.description || "",
          logo: brand.logo || "",
          website: brand.website || "",
          country: brand.country || "",
          founded_year: brand.founded_year || undefined,
          is_active: brand.is_active,
        });
      } else {
        reset({
          name: "",
          description: "",
          logo: "",
          website: "",
          country: "",
          founded_year: undefined,
          is_active: true,
        });
      }
    }
  }, [open, brand, reset]);

  const onSubmit = async (data: BrandFormData) => {
    try {
      // Convert empty strings to undefined for optional fields
      const cleanData = {
        ...data,
        description: data.description || undefined,
        logo: data.logo || undefined,
        website: data.website || undefined,
        country: data.country || undefined,
        founded_year: data.founded_year || undefined,
      };

      if (isEditing && brand) {
        await updateBrandMutation.mutateAsync({
          id: brand.id,
          ...cleanData,
        });
        toast({
          title: "Success",
          description: "Brand updated successfully",
        });
      } else {
        await createBrandMutation.mutateAsync(cleanData);
        toast({
          title: "Success",
          description: "Brand created successfully",
        });
      }

      onSuccess?.();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEditing ? "update" : "create"} brand`,
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] neo-shadow">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Brand" : "Create New Brand"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update the brand information below." 
              : "Add a new brand to organize your products by manufacturer."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Brand Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Brand Name *</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="e.g., Wilson, Nike, Adidas"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            {/* Country */}
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                {...register("country")}
                placeholder="e.g., USA, Germany, Japan"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.country && (
                <p className="text-sm text-destructive">{errors.country.message}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Brief description of the brand..."
              rows={3}
              className="neo-shadow-inset"
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Logo URL */}
            <div className="space-y-2">
              <Label htmlFor="logo">Logo URL</Label>
              <Input
                id="logo"
                {...register("logo")}
                placeholder="https://example.com/logo.png"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.logo && (
                <p className="text-sm text-destructive">{errors.logo.message}</p>
              )}
            </div>

            {/* Website */}
            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                {...register("website")}
                placeholder="https://example.com"
                className="min-h-[44px] neo-shadow-inset"
              />
              {errors.website && (
                <p className="text-sm text-destructive">{errors.website.message}</p>
              )}
            </div>
          </div>

          {/* Founded Year */}
          <div className="space-y-2">
            <Label htmlFor="founded_year">Founded Year</Label>
            <Input
              id="founded_year"
              type="number"
              {...register("founded_year", { 
                setValueAs: (value) => value === "" ? undefined : parseInt(value) 
              })}
              placeholder="e.g., 1914"
              min="1800"
              max={new Date().getFullYear()}
              className="min-h-[44px] neo-shadow-inset"
            />
            {errors.founded_year && (
              <p className="text-sm text-destructive">{errors.founded_year.message}</p>
            )}
          </div>

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={isActive}
              onCheckedChange={(checked) => setValue("is_active", checked)}
              className="neo-shadow"
            />
            <Label htmlFor="is_active">
              Active Brand
              <span className="block text-sm text-muted-foreground">
                Inactive brands won't appear in product forms
              </span>
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="min-h-[44px] neo-shadow hover:neo-shadow-light transition-neo"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || createBrandMutation.isPending || updateBrandMutation.isPending}
              className="min-h-[44px] neo-shadow hover:neo-shadow-light transition-neo"
            >
              {isSubmitting || createBrandMutation.isPending || updateBrandMutation.isPending
                ? (isEditing ? "Updating..." : "Creating...")
                : (isEditing ? "Update Brand" : "Create Brand")
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
