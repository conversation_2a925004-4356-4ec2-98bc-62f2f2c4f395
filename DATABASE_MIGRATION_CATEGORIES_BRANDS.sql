-- =====================================================
-- MIGRATION: UPGRADE CATEGORIES AND CREATE BRANDS TABLES
-- Safe migration for existing Tennis Whisperer database
-- Run this in your Supabase SQL Editor
-- =====================================================

-- STEP 1: Backup existing categories data
CREATE TABLE IF NOT EXISTS public.categories_backup AS 
SELECT * FROM public.categories;

-- STEP 2: Add new columns to existing categories table
ALTER TABLE public.categories 
ADD COLUMN IF NOT EXISTS slug TEXT,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS parent_id UUID,
ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS product_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- STEP 3: Update existing data with slugs and set defaults
UPDATE public.categories 
SET 
    slug = LOWER(REPLACE(REPLACE(name, ' ', '-'), '&', 'and')),
    is_active = true,
    sort_order = 0,
    product_count = COALESCE(count, 0),
    metadata = '{}'::jsonb
WHERE slug IS NULL;

-- STEP 4: Add constraints after data is populated
ALTER TABLE public.categories 
ADD CONSTRAINT categories_slug_unique UNIQUE (slug);

-- Add foreign key constraint for parent_id
ALTER TABLE public.categories 
ADD CONSTRAINT categories_parent_id_fkey 
FOREIGN KEY (parent_id) REFERENCES public.categories(id) ON DELETE SET NULL;

-- STEP 5: Create brands table
CREATE TABLE IF NOT EXISTS public.brands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    logo TEXT,
    website TEXT,
    country TEXT,
    founded_year INTEGER,
    is_active BOOLEAN DEFAULT true,
    product_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 6: Add brand_id and category_id to products table
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS brand_id UUID,
ADD COLUMN IF NOT EXISTS category_id UUID;

-- Add foreign key constraints
ALTER TABLE public.products 
ADD CONSTRAINT products_brand_id_fkey 
FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE SET NULL;

ALTER TABLE public.products 
ADD CONSTRAINT products_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES public.categories(id) ON DELETE SET NULL;

-- STEP 7: Migrate existing product categories to new system
UPDATE public.products 
SET category_id = (
    SELECT id FROM public.categories 
    WHERE LOWER(categories.name) = LOWER(products.category)
    LIMIT 1
)
WHERE category_id IS NULL AND category IS NOT NULL;

-- STEP 8: Enable Row Level Security
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.brands ENABLE ROW LEVEL SECURITY;

-- STEP 9: Create RLS Policies
-- Categories Policies
DROP POLICY IF EXISTS "Anyone can view active categories" ON public.categories;
DROP POLICY IF EXISTS "Admins can view all categories" ON public.categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON public.categories;

CREATE POLICY "Anyone can view active categories" ON public.categories
    FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can view all categories" ON public.categories
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Admins can manage categories" ON public.categories
    FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'));

-- Brands Policies
CREATE POLICY "Anyone can view active brands" ON public.brands
    FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can view all brands" ON public.brands
    FOR SELECT USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Admins can manage brands" ON public.brands
    FOR ALL USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'));

-- STEP 10: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_categories_slug ON public.categories(slug);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON public.categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_is_active ON public.categories(is_active);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON public.categories(sort_order);

CREATE INDEX IF NOT EXISTS idx_brands_slug ON public.brands(slug);
CREATE INDEX IF NOT EXISTS idx_brands_is_active ON public.brands(is_active);
CREATE INDEX IF NOT EXISTS idx_brands_name ON public.brands(name);

CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_brand_id ON public.products(brand_id);

-- STEP 11: Create functions for automatic counts
CREATE OR REPLACE FUNCTION public.update_category_product_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update count for old category (if changed)
    IF TG_OP = 'UPDATE' AND OLD.category_id IS DISTINCT FROM NEW.category_id THEN
        UPDATE public.categories
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE category_id = OLD.category_id
        )
        WHERE id = OLD.category_id;
    END IF;

    -- Update count for new category
    IF TG_OP IN ('INSERT', 'UPDATE') AND NEW.category_id IS NOT NULL THEN
        UPDATE public.categories
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE category_id = NEW.category_id
        )
        WHERE id = NEW.category_id;
    END IF;

    -- Update count for deleted product's category
    IF TG_OP = 'DELETE' AND OLD.category_id IS NOT NULL THEN
        UPDATE public.categories
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE category_id = OLD.category_id
        )
        WHERE id = OLD.category_id;
        RETURN OLD;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.update_brand_product_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update count for old brand (if changed)
    IF TG_OP = 'UPDATE' AND OLD.brand_id IS DISTINCT FROM NEW.brand_id THEN
        UPDATE public.brands
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE brand_id = OLD.brand_id
        )
        WHERE id = OLD.brand_id;
    END IF;

    -- Update count for new brand
    IF TG_OP IN ('INSERT', 'UPDATE') AND NEW.brand_id IS NOT NULL THEN
        UPDATE public.brands
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE brand_id = NEW.brand_id
        )
        WHERE id = NEW.brand_id;
    END IF;

    -- Update count for deleted product's brand
    IF TG_OP = 'DELETE' AND OLD.brand_id IS NOT NULL THEN
        UPDATE public.brands
        SET product_count = (
            SELECT COUNT(*) FROM public.products
            WHERE brand_id = OLD.brand_id
        )
        WHERE id = OLD.brand_id;
        RETURN OLD;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 12: Create triggers
DROP TRIGGER IF EXISTS category_product_count_trigger ON public.products;
DROP TRIGGER IF EXISTS brand_product_count_trigger ON public.products;

CREATE TRIGGER category_product_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.products
    FOR EACH ROW EXECUTE FUNCTION public.update_category_product_count();

CREATE TRIGGER brand_product_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.products
    FOR EACH ROW EXECUTE FUNCTION public.update_brand_product_count();

-- STEP 13: Insert sample tennis categories
INSERT INTO public.categories (id, name, slug, description, sort_order, is_active) VALUES
('cat-rackets', 'Tennis Rackets', 'tennis-rackets', 'Professional and recreational tennis rackets', 1, true),
('cat-strings', 'Tennis Strings', 'tennis-strings', 'High-quality tennis strings for all playing styles', 2, true),
('cat-balls', 'Tennis Balls', 'tennis-balls', 'Tournament and practice tennis balls', 3, true),
('cat-shoes', 'Tennis Shoes', 'tennis-shoes', 'Court shoes designed for tennis performance', 4, true),
('cat-apparel', 'Tennis Apparel', 'tennis-apparel', 'Tennis clothing and accessories', 5, true),
('cat-bags', 'Tennis Bags', 'tennis-bags', 'Racket bags and tennis equipment storage', 6, true),
('cat-accessories', 'Accessories', 'accessories', 'Grips, dampeners, and other tennis accessories', 7, true),
('cat-training', 'Training Equipment', 'training-equipment', 'Training aids and practice equipment', 8, true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    slug = EXCLUDED.slug,
    description = EXCLUDED.description,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active;

-- STEP 14: Insert sample tennis brands
INSERT INTO public.brands (id, name, slug, description, website, country, is_active) VALUES
('brand-wilson', 'Wilson', 'wilson', 'Leading tennis equipment manufacturer', 'https://www.wilson.com', 'USA', true),
('brand-babolat', 'Babolat', 'babolat', 'French tennis equipment specialist', 'https://www.babolat.com', 'France', true),
('brand-head', 'HEAD', 'head', 'Austrian sports equipment company', 'https://www.head.com', 'Austria', true),
('brand-yonex', 'Yonex', 'yonex', 'Japanese racket sports equipment', 'https://www.yonex.com', 'Japan', true),
('brand-prince', 'Prince', 'prince', 'American tennis equipment brand', 'https://www.prince.com', 'USA', true),
('brand-tecnifibre', 'Tecnifibre', 'tecnifibre', 'French tennis string and equipment', 'https://www.tecnifibre.com', 'France', true),
('brand-dunlop', 'Dunlop', 'dunlop', 'British sports equipment manufacturer', 'https://www.dunlopsport.com', 'UK', true),
('brand-nike', 'Nike', 'nike', 'Global athletic apparel and footwear', 'https://www.nike.com', 'USA', true),
('brand-adidas', 'Adidas', 'adidas', 'German athletic apparel and footwear', 'https://www.adidas.com', 'Germany', true),
('brand-asics', 'ASICS', 'asics', 'Japanese athletic footwear company', 'https://www.asics.com', 'Japan', true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    slug = EXCLUDED.slug,
    description = EXCLUDED.description,
    website = EXCLUDED.website,
    country = EXCLUDED.country,
    is_active = EXCLUDED.is_active;

-- STEP 15: Update product counts
UPDATE public.categories 
SET product_count = (
    SELECT COUNT(*) FROM public.products 
    WHERE products.category_id = categories.id
);

UPDATE public.brands 
SET product_count = (
    SELECT COUNT(*) FROM public.products 
    WHERE products.brand_id = brands.id
);

-- STEP 16: Success message
RAISE NOTICE '✅ Categories and Brands migration completed successfully!';
RAISE NOTICE 'Enhanced categories table with new fields: slug, description, parent_id, sort_order, is_active, product_count, metadata';
RAISE NOTICE 'Created brands table with full brand management capabilities';
RAISE NOTICE 'Added category_id and brand_id foreign keys to products table';
RAISE NOTICE 'Migrated existing product categories to new system';
RAISE NOTICE 'Created RLS policies and performance indexes';
RAISE NOTICE 'Added automatic product count triggers';
RAISE NOTICE 'Inserted sample tennis categories and brands';
